import requests
from lxml import etree


class Spider():
    def __init__(self):
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'priority': 'u=0, i',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-full-version': '"140.0.7339.80"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"10.0.0"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            # 'cookie': 's=CgAD4ACBoxUaRM2Q3NTRjZWQxOTkwYWIxMDQ3MzMwMTcwZmZmZDU1YzFC6lxY; __uzma=3f0055ec-d9b3-4a87-8e1b-4c80a929efb6; __uzmb=1757672721; __uzme=1951; ak_bmsc=92FF47B037811A42A53F4481548E9F59~000000000000000000000000000000~YAAQLhQgF7q5zjSZAQAAD091PR0quF4Z+/WApN0qPUWjlnXw88ZKXZln98j0kZjYrh+lDuf0oRKk8OiRbtmUU4ZVrmr1We1efDQvd32tTjPzZYOy189yfYzA5AAjf+jH60qM8LD03rWIvxNqeGoGNUmWqQtiNxPxFtOzUBnknIc/yAfhIUH25aaYKAinKwMH0By+F5Mpp1MipBpL9qf5GnHkEGkbY5mgn2hhatLCUeeVX+kzmqUWMd3ZnfQdtjCaw8UnK5zjvKy6KLUO9rg3kQE3tbR0EDwYcIirxgxAWIhlGT15uIuRTLXaJ6MyDqINN8oJuWXXwWlAYAhZ93ssmGt4ud5Cra9rILuMteoOWhZbWYbMt36RC97hlpo1pZBVKUuX9RFG8KqookY=; __ssds=3; __ssuzjsr3=a9be0cd8e; __uzmaj3=8de8ca27-be2d-401a-89f0-a70e2b175bfb; __uzmbj3=1757673013; __uzmlj3=HmO5N31iBPYBhqkDYe+jnF8d8heVg0xxV+w9KKVAuCw=; __uzmcj3=618511328679; __uzmdj3=1757673604; __uzmfj3=7f60004993187e-9768-4a6c-9d9c-881d90653b781757673013364590818-e8c97b8b9e5233ba13; __uzmc=652503162006; __uzmd=1757674165; __uzmf=7f60004993187e-9768-4a6c-9d9c-881d90653b7817576727216101444366-00b657764086bab731; __deba=tWzgiSkOPaSSgQMutovX9mtFDxHxW8B7VaVs7xNaSN4ew3Qmw-hXVQTWghnGVtyRTuXxAonI5jAODvNoUf-9tpzGOLGSw2qHQNKiB9Hhs_SKUx8Z4sNV5fWnkNLZuFX4NcWdCL8lTEKtmoaY30zGlQ==; nonsession=BAQAAAZhdx0gSAAaAADMABmqlLwEzNjEwMDAAygAgbIZigTNkNzU0Y2VkMTk5MGFiMTA0NzMzMDE3MGZmZmQ1NWMxAMsAAmjEAokyNhDFrFOU5tc8fZys6PffJ6M0Y0va; ds2=amsg/3d754ced1990ab1047330170fffd55c1^; ebay=%5Ejs%3D1%5Esbf%3D%23000000%5E; dp1=bpbf/#e000000000000000006c866290^bl/CN6c866281^',
        }

    def get_detail_url(self,url):
        res = requests.get(url,headers=self.headers)
        return res.text






URL_LIST = [
    'https://www.ebay.com/itm/196358027371',
    'https://www.ebay.com/itm/225544809196',
    'https://www.ebay.co.uk/itm/293582338849'
            ]

if __name__ == '__main__':
    spider = Spider()
    for url in URL_LIST[:1]:
        html = spider.get_detail_url(url)
        print(html)